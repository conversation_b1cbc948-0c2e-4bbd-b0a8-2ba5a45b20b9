# Executable Attendance Requests with Real Authentication

## Overview
This project generates and executes attendance requests for all employees and schedules using **real Bearer tokens** from the authentication system. All curl commands are ready to execute without manual token replacement.

## 🎯 What Was Generated

### ✅ **Complete Solution with Real Authentication**
- **168 attendance requests** (8 employees × 21 schedules)
- **Real Bearer tokens** extracted from live authentication
- **Executable curl commands** ready to run immediately
- **No placeholder tokens** - everything is production-ready

## 📁 Generated Files

### 1. **`generate_attendance_with_auth.py`** - Main Generator Script
- **Purpose**: Standalone Python script that generates all attendance requests
- **Features**:
  - Authenticates with real credentials (`<EMAIL>` / `admin`)
  - Fetches live employee, schedule, and location data
  - Generates 168 curl commands with real Bearer tokens
  - Saves output to `executable_attendance_requests_with_auth.txt`

**Usage:**
```bash
python generate_attendance_with_auth.py
```

### 2. **`executable_attendance_requests_with_auth.txt`** - Ready-to-Execute Commands
- **Purpose**: Contains all 168 curl commands with real authentication
- **Features**:
  - Real Bearer token: `eyJhbGciOiJIUzI1NiJ9.eyJ4X3dobyI6...`
  - Proper employee account IDs
  - Realistic attendance timestamps (±15 minutes from scheduled times)
  - Correct location coordinates (-8.**************, 120.**************)

**Sample Command:**
```bash
# Utami Nurhalim - Clock in (Day 1) - 2025-06-23 06:50:08
curl --location 'http://localhost:9765/api/attendance/v1' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJ4X3dobyI6...' \
--data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-23 06:50:08",...}'
```

### 3. **`execute_attendance_requests.sh`** - Automated Execution Script
- **Purpose**: Bash script to execute all curl commands automatically
- **Features**:
  - Counts total commands (168)
  - Asks for confirmation before execution
  - Shows progress with employee names
  - Tracks success/error counts
  - Includes small delays to avoid server overload

**Usage:**
```bash
./execute_attendance_requests.sh
```

## 🚀 How to Use

### Option 1: Generate Fresh Requests (Recommended)
```bash
# Generate new requests with fresh authentication
python generate_attendance_with_auth.py

# Execute all requests automatically
./execute_attendance_requests.sh
```

### Option 2: Execute Existing Requests
```bash
# Execute the pre-generated requests
./execute_attendance_requests.sh
```

### Option 3: Manual Execution
```bash
# Execute individual commands manually
curl --location 'http://localhost:9765/api/attendance/v1' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...' \
--data '{"account_id":"...","date_time":"...","time_table_id":"...","location_id":"...","image_url":"...","lat":...,"lng":...}'
```

## 📊 Data Details

### **Employee Coverage (8 employees)**
1. Utami Nurhalim (5970FF7088C54094A751BE29DFC6A930)
2. Rina Permadi (D1DCB8C61979434493A237363013D026)
3. Rina Permadi (475E542E95654DC38A78969802284171) - Different account
4. Wahyu Lestari (DB3A8625C1B64CD89935677A56981A6E)
5. Putra Setiawan (6D95AFF1ECD647B68BD2B37F0B0A7D55)
6. Eka Hidayat (D3EB0A34B74D4B4CAA9CB72E80C06904)
7. Utami Halim (E97700D03D5C41B2A951403407C56F0C)
8. kevin sanjaya (0AC2B9213FEF433A82DEF9A41ED55E6D)

### **Schedule Coverage (21 schedules across 7 days)**
Each day has 3 attendance points:
- **Clock in**: 07:00:00 - 08:00:00
- **In day**: 12:00:00 - 13:00:00 (lunch break return)
- **Clock out**: 17:00:00 - 18:00:00

**Week Coverage:**
- Monday (Day 1): 2025-06-23
- Tuesday (Day 2): 2025-06-24
- Wednesday (Day 3): 2025-06-25
- Thursday (Day 4): 2025-06-26
- Friday (Day 5): 2025-06-27
- Saturday (Day 6): 2025-06-28
- Sunday (Day 7): 2025-06-29

### **Location Data**
- **Name**: Kantor (Office)
- **ID**: 628C9E37CD02456B857419BF4120CCBA
- **Coordinates**: lat: -8.**************, lng: 120.**************
- **Radius**: 200 meters

## 🔐 Authentication Details

### **Real Bearer Token Used**
```
eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.t8q84A3zjsSXToPavbALZZmHnuZ-WbWVNGD3CBtrqu0
```

### **Authentication Source**
- **Login URL**: https://backend.kodekata.web.id/api/auth/v1/admin/sign-in
- **Credentials**: <EMAIL> / admin
- **Role**: SUPER_ADMIN
- **Work Unit**: Unit Kerja 1 (3711AD05A5234B46858CB9CA5FE32ECD)

## ⚡ Quick Start

1. **Generate requests with real authentication:**
   ```bash
   python generate_attendance_with_auth.py
   ```

2. **Execute all 168 requests automatically:**
   ```bash
   ./execute_attendance_requests.sh
   ```

3. **Monitor the execution:**
   - The script shows progress for each employee
   - Displays success/error counts
   - Includes small delays to avoid server overload

## 🎯 Key Advantages

### ✅ **No Manual Token Replacement**
- All Bearer tokens are real and current
- No need to edit files or replace placeholders
- Commands are immediately executable

### ✅ **Complete Data Coverage**
- Every employee has attendance for every schedule
- Realistic time variations (±15 minutes from scheduled times)
- Proper geographic coordinates

### ✅ **Production Ready**
- Uses live authentication system
- Fetches current employee and schedule data
- Includes proper error handling and progress tracking

### ✅ **Easy Execution**
- Single command to generate all requests
- Automated execution with progress monitoring
- Clear success/error reporting

## 🔧 Troubleshooting

### **If authentication fails:**
```bash
# Regenerate with fresh authentication
python generate_attendance_with_auth.py
```

### **If server returns errors:**
- Check if the attendance API server is running on localhost:9765
- Verify the Bearer token hasn't expired
- Ensure the work unit and employee data is still valid

### **If execution is slow:**
- The script includes 0.1 second delays between requests
- This prevents server overload but can be adjusted in the bash script

## 📈 Expected Results

After successful execution, you should have:
- **168 attendance records** created in the system
- **Complete week coverage** for all 8 employees
- **Realistic attendance patterns** with proper time variations
- **All records linked** to the correct location and schedules

## 🎉 Success!

You now have a complete, executable attendance request system with real authentication that requires no manual intervention!
