#!/bin/bash

# Execute all attendance requests with real authentication
# This script runs all the curl commands from the generated file

echo "🚀 Starting attendance request execution..."
echo "📁 Reading commands from: executable_attendance_requests_with_auth.txt"
echo ""

# Check if the file exists
if [ ! -f "executable_attendance_requests_with_auth.txt" ]; then
    echo "❌ Error: executable_attendance_requests_with_auth.txt not found!"
    echo "Please run 'python generate_attendance_with_auth.py' first to generate the file."
    exit 1
fi

# Count total curl commands
total_commands=$(grep -c "^curl" executable_attendance_requests_with_auth.txt)
echo "📊 Total curl commands to execute: $total_commands"
echo ""

# Ask for confirmation
read -p "Do you want to execute all $total_commands attendance requests? (y/N): " confirm
if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "❌ Execution cancelled."
    exit 0
fi

echo ""
echo "⏳ Executing attendance requests..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Initialize counters
success_count=0
error_count=0
current_command=0

# Read the file and build complete curl commands
current_curl=""
current_comment=""
in_curl_command=false

while IFS= read -r line; do
    # Check if this is a comment line
    if [[ $line =~ ^#.*$ ]]; then
        current_comment="$line"
        continue
    fi

    # Skip empty lines
    if [[ -z "$line" ]]; then
        # If we were building a curl command, execute it now
        if [ "$in_curl_command" = true ] && [ -n "$current_curl" ]; then
            current_command=$((current_command + 1))

            # Extract employee info from comment
            employee_info=$(echo "$current_comment" | sed 's/^# //')

            echo "[$current_command/$total_commands] Executing: $employee_info"

            # Execute the complete curl command
            response=$(eval "$current_curl" 2>&1)
            exit_code=$?

            if [ $exit_code -eq 0 ]; then
                # Check if response contains success indicators
                if echo "$response" | grep -q '"success":true\|"status":"success"\|"code":200\|SUCCESS'; then
                    echo "  ✅ Success"
                    success_count=$((success_count + 1))
                else
                    echo "  ⚠️  Warning: Unexpected response - $response"
                    error_count=$((error_count + 1))
                fi
            else
                echo "  ❌ Error: $response"
                error_count=$((error_count + 1))
            fi

            # Small delay to avoid overwhelming the server
            sleep 0.1

            # Reset for next command
            current_curl=""
            in_curl_command=false
        fi
        continue
    fi

    # Check if line starts with curl
    if [[ $line =~ ^curl.*$ ]]; then
        in_curl_command=true
        current_curl="$line"
    elif [ "$in_curl_command" = true ]; then
        # This is a continuation line, append it
        current_curl="$current_curl $line"
    fi

done < executable_attendance_requests_with_auth.txt

# Handle the last command if the file doesn't end with an empty line
if [ "$in_curl_command" = true ] && [ -n "$current_curl" ]; then
    current_command=$((current_command + 1))

    employee_info=$(echo "$current_comment" | sed 's/^# //')

    echo "[$current_command/$total_commands] Executing: $employee_info"

    response=$(eval "$current_curl" 2>&1)
    exit_code=$?

    if [ $exit_code -eq 0 ]; then
        if echo "$response" | grep -q '"success":true\|"status":"success"\|"code":200\|SUCCESS'; then
            echo "  ✅ Success"
            success_count=$((success_count + 1))
        else
            echo "  ⚠️  Warning: Unexpected response - $response"
            error_count=$((error_count + 1))
        fi
    else
        echo "  ❌ Error: $response"
        error_count=$((error_count + 1))
    fi
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 Execution Summary:"
echo "   Total commands: $total_commands"
echo "   Successful: $success_count"
echo "   Errors/Warnings: $error_count"
echo ""

if [ $error_count -eq 0 ]; then
    echo "🎉 All attendance requests executed successfully!"
else
    echo "⚠️  Some requests had issues. Please check the output above."
fi

echo ""
echo "✅ Attendance request execution completed!"
