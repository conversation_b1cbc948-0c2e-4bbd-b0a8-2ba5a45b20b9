#!/usr/bin/env python3
"""
Final Attendance Request Executor - Python Version
Executes all 168 attendance requests using Python requests library.
No bash, no curl - pure Python implementation.
"""

import requests
import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Configuration
WORK_UNIT_ID = '3711AD05A5234B46858CB9CA5FE32ECD'
BASE_URL = "https://backend.kodekata.web.id/api"
SIGN_IN_URL = "/auth/v1/admin/sign-in"
ATTENDANCE_URL = "http://localhost:9765/api/attendance/v1"
IMAGE_URL = "https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png"

def authenticate():
    """Authenticate and get access token"""
    data = {"data": "<EMAIL>", "password": "admin"}
    response = requests.post(f"{BASE_URL}{SIGN_IN_URL}", json=data)
    return response.json()["response_data"]["access_token"]

def fetch_data(access_token):
    """Fetch employee, schedule, and location data"""
    headers = {'Authorization': f'Bearer {access_token}'}
    
    employee_data = requests.get(f'{BASE_URL}/employee/v1/work-unit/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    schedule_list = requests.get(f'{BASE_URL}/work-unit/v1/schedule/regular/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    location_list = requests.get(f'{BASE_URL}/work-unit/v1/location/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    
    return employee_data, schedule_list, location_list

def get_date_for_day(base_date: str, target_day: int) -> str:
    """Get date for specific day of week"""
    base = datetime.strptime(base_date, "%Y-%m-%d")
    days_since_monday = base.weekday()
    monday_of_week = base - timedelta(days=days_since_monday)
    target_date = monday_of_week + timedelta(days=target_day - 1)
    return target_date.strftime("%Y-%m-%d")

def generate_attendance_time(schedule_time: str, date: str) -> str:
    """Generate realistic attendance time"""
    time_obj = datetime.strptime(schedule_time, "%H:%M:%S").time()
    variation_minutes = random.randint(-15, 15)
    dt = datetime.combine(datetime.strptime(date, "%Y-%m-%d").date(), time_obj)
    dt += timedelta(minutes=variation_minutes, seconds=random.randint(0, 59))
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def execute_attendance_request(request_data):
    """Execute single attendance request"""
    data = {
        "account_id": request_data['account_id'],
        "date_time": request_data['date_time'],
        "time_table_id": request_data['time_table_id'],
        "location_id": request_data['location_id'],
        "image_url": IMAGE_URL,
        "lat": request_data['lat'],
        "lng": request_data['lng']
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {request_data["bearer_token"]}'
    }
    
    try:
        response = requests.post(ATTENDANCE_URL, json=data, headers=headers)
        response_data = response.json()
        
        if response.status_code == 200 and response_data.get('success') == True:
            return {"status": "success", "response": response_data}
        elif 'ALREADY_ATTENDANCE' in str(response_data):
            return {"status": "already_exists", "response": response_data}
        else:
            return {"status": "warning", "response": response_data}
            
    except Exception as e:
        return {"status": "error", "response": str(e)}

def main():
    """Main execution function"""
    print("🎯 Final Attendance Request Executor - Python Version")
    print("=" * 60)
    
    # Step 1: Authentication
    print("🔐 Step 1: Authenticating...")
    try:
        access_token = authenticate()
        print(f"✅ Authentication successful! Token: {access_token[:20]}...")
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False
    
    # Step 2: Fetch data
    print("\n📊 Step 2: Fetching data...")
    try:
        employee_data, schedule_list, location_list = fetch_data(access_token)
        print(f"✅ Data fetched successfully!")
        print(f"   - Employees: {len(employee_data)}")
        print(f"   - Schedules: {len(schedule_list)}")
        print(f"   - Locations: {len(location_list)}")
    except Exception as e:
        print(f"❌ Data fetching failed: {e}")
        return False
    
    # Step 3: Prepare requests
    base_date = "2025-04-23"
    total_requests = len(employee_data) * len(schedule_list)
    
    print(f"\n📝 Step 3: Preparing {total_requests} requests...")
    print(f"   - Base date: {base_date}")
    print(f"   - Date range: Monday (2025-06-23) to Sunday (2025-06-29)")
    
    all_requests = []
    location = location_list[0]
    
    for employee in employee_data:
        for schedule in schedule_list:
            actual_date = get_date_for_day(base_date, schedule['day'])
            attendance_time = generate_attendance_time(schedule['start_time'], actual_date)
            
            request_data = {
                'account_id': employee['account_id'],
                'date_time': attendance_time,
                'time_table_id': schedule['id'],
                'location_id': location['id'],
                'lat': location['lat'],
                'lng': location['lng'],
                'account_name': employee['name'],
                'schedule_name': f"{schedule['name']} (Day {schedule['day']})",
                'bearer_token': access_token
            }
            all_requests.append(request_data)
    
    print(f"✅ {len(all_requests)} requests prepared")
    
    # Step 4: Execute requests
    print(f"\n🚀 Step 4: Executing {len(all_requests)} attendance requests...")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    results = {
        'success': 0,
        'already_exists': 0,
        'warning': 0,
        'error': 0,
        'details': []
    }
    
    start_time = time.time()
    
    for i, request_data in enumerate(all_requests, 1):
        # Progress indicator
        if i % 10 == 0 or i <= 10:
            print(f"[{i:3d}/{len(all_requests)}] Processing...")
        
        result = execute_attendance_request(request_data)
        results[result['status']] += 1
        results['details'].append({
            'employee': request_data['account_name'],
            'schedule': request_data['schedule_name'],
            'status': result['status'],
            'response': result['response']
        })
        
        # Small delay to avoid overwhelming server
        time.sleep(0.02)
    
    execution_time = time.time() - start_time
    
    # Step 5: Results summary
    print("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("📊 EXECUTION SUMMARY")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"   Total requests executed: {len(all_requests)}")
    print(f"   ✅ Successful (new records): {results['success']}")
    print(f"   ⚠️  Already exists: {results['already_exists']}")
    print(f"   ⚠️  Warnings: {results['warning']}")
    print(f"   ❌ Errors: {results['error']}")
    print(f"   ⏱️  Execution time: {execution_time:.2f} seconds")
    
    # Success rate calculation
    total_completed = results['success'] + results['already_exists']
    success_rate = (total_completed / len(all_requests)) * 100
    
    print(f"\n🎯 COMPLETION RATE: {success_rate:.1f}%")
    
    if results['error'] == 0:
        print("\n🎉 ALL REQUESTS COMPLETED SUCCESSFULLY!")
        if results['already_exists'] > 0:
            print(f"✅ {results['success']} new attendance records created")
            print(f"ℹ️  {results['already_exists']} records already existed")
        else:
            print("✅ All 168 attendance records have been created in the system")
    else:
        print(f"\n⚠️  {results['error']} requests failed")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"attendance_execution_results_{timestamp}.txt"
    
    with open(results_file, 'w') as f:
        f.write(f"Attendance Execution Results - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"SUMMARY:\n")
        f.write(f"Total requests: {len(all_requests)}\n")
        f.write(f"Successful: {results['success']}\n")
        f.write(f"Already exists: {results['already_exists']}\n")
        f.write(f"Warnings: {results['warning']}\n")
        f.write(f"Errors: {results['error']}\n")
        f.write(f"Execution time: {execution_time:.2f} seconds\n")
        f.write(f"Success rate: {success_rate:.1f}%\n\n")
        
        f.write("DETAILED RESULTS:\n")
        f.write("-" * 80 + "\n")
        
        for i, detail in enumerate(results['details'], 1):
            f.write(f"{i:3d}. {detail['employee']} - {detail['schedule']}\n")
            f.write(f"     Status: {detail['status']}\n")
            f.write(f"     Response: {detail['response']}\n\n")
    
    print(f"\n📁 Detailed results saved to: {results_file}")
    
    print("\n" + "=" * 60)
    print("🎯 Attendance Request Execution Completed!")
    print("=" * 60)
    
    return results

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Execution interrupted by user.")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
