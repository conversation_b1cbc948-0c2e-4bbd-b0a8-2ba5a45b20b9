#!/usr/bin/env python3
"""
Final Attendance Request Executor - Full Year 2024 Version
Generates attendance records for the entire year 2024 (all working days).
Covers ~6,240 requests (8 employees × 3 checkpoints × ~260 working days).
No bash, no curl - pure Python implementation.
"""

import requests
import random
import time
from datetime import datetime, timedelta, date
from typing import List, Dict, Any
import calendar

# Configuration
WORK_UNIT_ID = '3711AD05A5234B46858CB9CA5FE32ECD'
BASE_URL = "https://backend.kodekata.web.id/api"
SIGN_IN_URL = "/auth/v1/admin/sign-in"
ATTENDANCE_URL = "http://localhost:9765/api/attendance/v1"
IMAGE_URL = "https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png"

def authenticate():
    """Authenticate and get access token"""
    data = {"data": "<EMAIL>", "password": "admin"}
    response = requests.post(f"{BASE_URL}{SIGN_IN_URL}", json=data)
    return response.json()["response_data"]["access_token"]

def fetch_data(access_token):
    """Fetch employee, schedule, and location data"""
    headers = {'Authorization': f'Bearer {access_token}'}
    
    employee_data = requests.get(f'{BASE_URL}/employee/v1/work-unit/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    schedule_list = requests.get(f'{BASE_URL}/work-unit/v1/schedule/regular/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    location_list = requests.get(f'{BASE_URL}/work-unit/v1/location/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    
    return employee_data, schedule_list, location_list

def get_working_days_2024():
    """Generate all working days (Monday-Friday) for the year 2024"""
    working_days = []

    # Major holidays to skip (optional - can be customized)
    holidays_2024 = {
        date(2024, 1, 1),   # New Year's Day
        date(2024, 7, 4),   # Independence Day (if applicable)
        date(2024, 12, 25), # Christmas Day
        # Add more holidays as needed
    }

    start_date = date(2024, 1, 1)
    end_date = date(2024, 12, 31)

    current_date = start_date
    while current_date <= end_date:
        # Check if it's a weekday (Monday=0, Sunday=6)
        if current_date.weekday() < 5:  # Monday to Friday
            # Skip holidays
            if current_date not in holidays_2024:
                working_days.append(current_date)
        current_date += timedelta(days=1)

    return working_days

def get_month_name(month_num):
    """Get month name from month number"""
    return calendar.month_name[month_num]

def calculate_total_requests(employee_count, working_days_count, checkpoints_per_day=3):
    """Calculate total number of requests to be generated"""
    return employee_count * working_days_count * checkpoints_per_day

def generate_attendance_time(schedule_time: str, target_date: date) -> str:
    """Generate realistic attendance time for a specific date"""
    time_obj = datetime.strptime(schedule_time, "%H:%M:%S").time()
    variation_minutes = random.randint(-15, 15)
    dt = datetime.combine(target_date, time_obj)
    dt += timedelta(minutes=variation_minutes, seconds=random.randint(0, 59))
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def execute_attendance_request(request_data):
    """Execute single attendance request"""
    data = {
        "account_id": request_data['account_id'],
        "date_time": request_data['date_time'],
        "time_table_id": request_data['time_table_id'],
        "location_id": request_data['location_id'],
        "image_url": IMAGE_URL,
        "lat": request_data['lat'],
        "lng": request_data['lng']
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {request_data["bearer_token"]}'
    }
    
    try:
        response = requests.post(ATTENDANCE_URL, json=data, headers=headers)
        response_data = response.json()
        
        if response.status_code == 200 and response_data.get('success') == True:
            return {"status": "success", "response": response_data}
        elif 'ALREADY_ATTENDANCE' in str(response_data):
            return {"status": "already_exists", "response": response_data}
        else:
            return {"status": "warning", "response": response_data}
            
    except Exception as e:
        return {"status": "error", "response": str(e)}

def main():
    """Main execution function for full year 2024"""
    print("🎯 Final Attendance Request Executor - Full Year 2024")
    print("=" * 70)

    # Step 1: Authentication
    print("🔐 Step 1: Authenticating...")
    try:
        access_token = authenticate()
        print(f"✅ Authentication successful! Token: {access_token[:20]}...")
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False

    # Step 2: Fetch data
    print("\n📊 Step 2: Fetching data...")
    try:
        employee_data, schedule_list, location_list = fetch_data(access_token)
        print(f"✅ Data fetched successfully!")
        print(f"   - Employees: {len(employee_data)}")
        print(f"   - Schedules: {len(schedule_list)}")
        print(f"   - Locations: {len(location_list)}")
    except Exception as e:
        print(f"❌ Data fetching failed: {e}")
        return False

    # Step 3: Calculate working days and total requests
    print("\n📅 Step 3: Calculating working days for 2024...")
    working_days = get_working_days_2024()

    # Filter schedules to only include the 3 main checkpoints
    # Debug: print all schedule names to see what's available
    print(f"   - Available schedules: {[s['name'] for s in schedule_list]}")

    main_schedules = [s for s in schedule_list if s['name'].lower() in ['clock in', 'in day', 'clock out']]

    # If no exact matches, try partial matches
    if len(main_schedules) == 0:
        main_schedules = [s for s in schedule_list if any(keyword in s['name'].lower()
                         for keyword in ['clock', 'in', 'out'])]

    # If still no matches, use first 3 schedules as fallback
    if len(main_schedules) == 0:
        main_schedules = schedule_list[:3]
        print(f"   - Using first 3 schedules as fallback: {[s['name'] for s in main_schedules]}")
    else:
        print(f"   - Selected schedules: {[s['name'] for s in main_schedules]}")

    total_requests = calculate_total_requests(len(employee_data), len(working_days), len(main_schedules))

    print(f"✅ Working days calculated:")
    print(f"   - Total working days in 2024: {len(working_days)}")
    print(f"   - Date range: {working_days[0]} to {working_days[-1]}")
    print(f"   - Checkpoints per day: {len(main_schedules)} (Clock in, In day, Clock out)")
    print(f"   - Total requests to generate: {total_requests:,}")

    # Estimate execution time
    estimated_time = total_requests * 0.02  # 0.02 seconds per request
    estimated_minutes = estimated_time / 60
    print(f"   - Estimated execution time: {estimated_minutes:.1f} minutes")

    # Ask for confirmation
    input(f"\n⚠️  This will generate {total_requests:,} attendance requests for the entire year 2024.\n")
    confirm = input("   Do you want to proceed? (yes/no): ")
    if confirm.lower() not in ['yes', 'y']:
        print("❌ Execution cancelled.")
        return False

    # Step 4: Prepare requests
    print(f"\n📝 Step 4: Preparing {total_requests:,} requests...")

    all_requests = []
    location = location_list[0]

    # Group working days by month for better progress tracking
    days_by_month = {}
    for working_day in working_days:
        month_key = working_day.strftime("%Y-%m")
        if month_key not in days_by_month:
            days_by_month[month_key] = []
        days_by_month[month_key].append(working_day)

    print(f"✅ Organizing by months: {len(days_by_month)} months to process")

    for month_key, month_days in days_by_month.items():
        year, month = month_key.split('-')
        month_name = get_month_name(int(month))
        print(f"   - {month_name} {year}: {len(month_days)} working days")

    # Generate requests for each employee, each working day, each checkpoint
    for employee in employee_data:
        for working_day in working_days:
            for schedule in main_schedules:
                attendance_time = generate_attendance_time(schedule['start_time'], working_day)

                request_data = {
                    'account_id': employee['account_id'],
                    'date_time': attendance_time,
                    'time_table_id': schedule['id'],
                    'location_id': location['id'],
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'account_name': employee['name'],
                    'schedule_name': f"{schedule['name']} ({working_day.strftime('%Y-%m-%d')})",
                    'bearer_token': access_token,
                    'working_day': working_day,
                    'month': working_day.strftime("%Y-%m")
                }
                all_requests.append(request_data)

    print(f"✅ {len(all_requests):,} requests prepared")
    
    # Step 5: Execute requests
    print(f"\n🚀 Step 5: Executing {len(all_requests):,} attendance requests...")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

    results = {
        'success': 0,
        'already_exists': 0,
        'warning': 0,
        'error': 0,
        'details': [],
        'monthly_stats': {}
    }

    start_time = time.time()
    current_month = None
    month_start_time = time.time()
    month_count = 0

    for i, request_data in enumerate(all_requests, 1):
        # Track monthly progress
        request_month = request_data['month']
        if current_month != request_month:
            if current_month is not None:
                month_time = time.time() - month_start_time
                print(f"   ✅ {current_month} completed in {month_time:.1f}s")

            current_month = request_month
            month_start_time = time.time()
            month_count += 1
            year, month = request_month.split('-')
            month_name = get_month_name(int(month))
            print(f"\n📅 Processing {month_name} {year} (Month {month_count}/12)...")

        # Progress indicator for large datasets
        if i % 100 == 0 or i <= 50:
            elapsed = time.time() - start_time
            rate = i / elapsed if elapsed > 0 else 0
            remaining = (len(all_requests) - i) / rate if rate > 0 else 0
            print(f"   [{i:5d}/{len(all_requests):,}] Progress: {(i/len(all_requests)*100):.1f}% | "
                  f"Rate: {rate:.1f}/s | ETA: {remaining/60:.1f}m")

        result = execute_attendance_request(request_data)
        results[result['status']] += 1

        # Track monthly statistics
        if request_month not in results['monthly_stats']:
            results['monthly_stats'][request_month] = {
                'success': 0, 'already_exists': 0, 'warning': 0, 'error': 0
            }
        results['monthly_stats'][request_month][result['status']] += 1

        results['details'].append({
            'employee': request_data['account_name'],
            'schedule': request_data['schedule_name'],
            'status': result['status'],
            'response': result['response'],
            'month': request_month
        })

        # Smaller delay for large datasets
        time.sleep(0.01)
    
    execution_time = time.time() - start_time

    # Complete final month
    if current_month is not None:
        month_time = time.time() - month_start_time
        print(f"   ✅ {current_month} completed in {month_time:.1f}s")

    # Step 6: Results summary
    print("\n" + "━" * 80)
    print("📊 EXECUTION SUMMARY - FULL YEAR 2024")
    print("━" * 80)
    print(f"   Total requests executed: {len(all_requests):,}")
    print(f"   ✅ Successful (new records): {results['success']:,}")
    print(f"   ⚠️  Already exists: {results['already_exists']:,}")
    print(f"   ⚠️  Warnings: {results['warning']:,}")
    print(f"   ❌ Errors: {results['error']:,}")
    print(f"   ⏱️  Total execution time: {execution_time/60:.1f} minutes ({execution_time:.1f} seconds)")

    # Success rate calculation
    total_completed = results['success'] + results['already_exists']
    success_rate = (total_completed / len(all_requests)) * 100

    print(f"\n🎯 COMPLETION RATE: {success_rate:.1f}%")

    # Monthly breakdown
    print(f"\n📅 MONTHLY BREAKDOWN:")
    print("-" * 50)
    for month_key in sorted(results['monthly_stats'].keys()):
        year, month = month_key.split('-')
        month_name = get_month_name(int(month))
        stats = results['monthly_stats'][month_key]
        total_month = sum(stats.values())
        success_month = stats['success'] + stats['already_exists']
        print(f"   {month_name} {year}: {success_month:3d}/{total_month:3d} "
              f"({success_month/total_month*100:.1f}%) - "
              f"New: {stats['success']:3d}, Existing: {stats['already_exists']:3d}")

    if results['error'] == 0:
        print("\n🎉 ALL REQUESTS COMPLETED SUCCESSFULLY!")
        if results['already_exists'] > 0:
            print(f"✅ {results['success']:,} new attendance records created for 2024")
            print(f"ℹ️  {results['already_exists']:,} records already existed")
        else:
            print(f"✅ All {len(all_requests):,} attendance records have been created for 2024")
    else:
        print(f"\n⚠️  {results['error']:,} requests failed")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"attendance_2024_results_{timestamp}.txt"
    summary_file = f"attendance_2024_summary_{timestamp}.txt"

    # Save summary file
    with open(summary_file, 'w') as f:
        f.write(f"Attendance Execution Summary - Full Year 2024\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"OVERALL SUMMARY:\n")
        f.write(f"Total requests: {len(all_requests):,}\n")
        f.write(f"Successful: {results['success']:,}\n")
        f.write(f"Already exists: {results['already_exists']:,}\n")
        f.write(f"Warnings: {results['warning']:,}\n")
        f.write(f"Errors: {results['error']:,}\n")
        f.write(f"Execution time: {execution_time/60:.1f} minutes ({execution_time:.1f} seconds)\n")
        f.write(f"Success rate: {success_rate:.1f}%\n\n")

        f.write("MONTHLY BREAKDOWN:\n")
        f.write("-" * 50 + "\n")
        for month_key in sorted(results['monthly_stats'].keys()):
            year, month = month_key.split('-')
            month_name = get_month_name(int(month))
            stats = results['monthly_stats'][month_key]
            total_month = sum(stats.values())
            success_month = stats['success'] + stats['already_exists']
            f.write(f"{month_name} {year}: {success_month:3d}/{total_month:3d} "
                   f"({success_month/total_month*100:.1f}%) - "
                   f"New: {stats['success']:3d}, Existing: {stats['already_exists']:3d}, "
                   f"Warnings: {stats['warning']:3d}, Errors: {stats['error']:3d}\n")

    # Save detailed results (only if dataset is manageable)
    if len(all_requests) <= 10000:  # Only save detailed results for smaller datasets
        with open(results_file, 'w') as f:
            f.write(f"Attendance Detailed Results - Full Year 2024\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")

            for i, detail in enumerate(results['details'], 1):
                f.write(f"{i:5d}. {detail['employee']} - {detail['schedule']} ({detail['month']})\n")
                f.write(f"       Status: {detail['status']}\n")
                if detail['status'] in ['warning', 'error']:
                    f.write(f"       Response: {detail['response']}\n")
                f.write("\n")

        print(f"\n📁 Detailed results saved to: {results_file}")
    else:
        print(f"\n📁 Detailed results too large - summary only saved")

    print(f"📁 Summary saved to: {summary_file}")

    print("\n" + "=" * 80)
    print("🎯 Full Year 2024 Attendance Request Execution Completed!")
    print("=" * 80)
    
    return results

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Execution interrupted by user.")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
