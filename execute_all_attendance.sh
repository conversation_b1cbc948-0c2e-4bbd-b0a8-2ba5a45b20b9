#!/bin/bash

# Execute all 168 attendance requests with progress tracking
# This script runs the generated bash file and counts successes

echo "🚀 Starting execution of all 168 attendance requests..."
echo "📁 Using file: executable_attendance_requests_with_auth.txt"
echo ""

# Check if the file exists
if [ ! -f "executable_attendance_requests_with_auth.txt" ]; then
    echo "❌ Error: executable_attendance_requests_with_auth.txt not found!"
    echo "Please run 'python generate_attendance_with_auth.py' first to generate the file."
    exit 1
fi

# Ask for confirmation
read -p "Do you want to execute all 168 attendance requests? (y/N): " confirm
if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "❌ Execution cancelled."
    exit 0
fi

echo ""
echo "⏳ Executing all attendance requests..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Execute the bash file and count results
output=$(bash executable_attendance_requests_with_auth.txt 2>&1)
echo "$output"

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Count results
total_executions=$(echo "$output" | grep -c "Executing:")
success_count=$(echo "$output" | grep -c "✅ Success")
warning_count=$(echo "$output" | grep -c "⚠️ Warning")

echo "📊 Execution Summary:"
echo "   Total requests executed: $total_executions"
echo "   Successful: $success_count"
echo "   Warnings: $warning_count"
echo ""

if [ $warning_count -eq 0 ]; then
    echo "🎉 All attendance requests executed successfully!"
    echo "✅ All 168 attendance records have been created in the system."
else
    echo "⚠️  Some requests had warnings. Please check the output above."
fi

echo ""
echo "✅ Attendance request execution completed!"
echo "📋 You can now check the attendance records in your system."
