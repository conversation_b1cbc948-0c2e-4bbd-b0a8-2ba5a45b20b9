%pip install requests
%pip install pandas

import requests
import pandas as pd
import json

print("HELLO")

work_unit_id = '3711AD05A5234B46858CB9CA5FE32ECD'
base_url = "https://backend.kodekata.web.id/api"
sign_in_url = "/auth/v1/admin/sign-in"

data = {
    "data" : "<EMAIL>",
    "password" : "admin"
}
response = requests.post(f"{base_url}{sign_in_url}", json=data )
response_data =response.json()["response_data"]
access_token = response_data["access_token"]
print(access_token)

url = f'{base_url}/employee/v1/work-unit/{work_unit_id}'
url_schedule = f'{base_url}/work-unit/v1/schedule/regular/{work_unit_id}'
url_location = f'{base_url}/work-unit/v1/location/{work_unit_id}'

headers = {
    'Authorization': f'Bearer {access_token}'
}

response = requests.get(url, headers=headers)
dataList = response_data =response.json()["response_data"]

response_schedule = requests.get(url_schedule, headers=headers)
schedule_list = response_schedule.json()["response_data"]

response_location = requests.get(url_location, headers=headers)
location_list = response_location.json()["response_data"]



employee_data = response.json()["response_data"]
df_employee = pd.DataFrame(response_data)
df_employee.head()


print(f"employee", employee_data)

print(f"Shedule List {schedule_list}")

print(f"Shedule List {location_list}")
