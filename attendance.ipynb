%pip install requests
%pip install pandas

import requests
import pandas as pd
import json

print("HELLO")

work_unit_id = '3711AD05A5234B46858CB9CA5FE32ECD'
base_url = "https://backend.kodekata.web.id/api"
sign_in_url = "/auth/v1/admin/sign-in"

data = {
    "data" : "<EMAIL>",
    "password" : "admin"
}
response = requests.post(f"{base_url}{sign_in_url}", json=data )
response_data =response.json()["response_data"]
access_token = response_data["access_token"]
print(access_token)

url = f'{base_url}/employee/v1/work-unit/{work_unit_id}'
url_schedule = f'{base_url}/work-unit/v1/schedule/regular/{work_unit_id}'
url_location = f'{base_url}/work-unit/v1/location/{work_unit_id}'

headers = {
    'Authorization': f'Bearer {access_token}'
}

response = requests.get(url, headers=headers)
dataList = response_data =response.json()["response_data"]

response_schedule = requests.get(url_schedule, headers=headers)
schedule_list = response_schedule.json()["response_data"]

response_location = requests.get(url_location, headers=headers)
location_list = response_location.json()["response_data"]



employee_data = response.json()["response_data"]
df_employee = pd.DataFrame(response_data)
df_employee.head()


print(f"employee", employee_data)

print(f"Shedule List {schedule_list}")

print(f"Shedule List {location_list}")


# Generate attendance requests script with real authentication
import random
from datetime import datetime, timedelta
import json

def get_date_for_day(base_date: str, target_day: int) -> str:
    """
    Get the date string for a specific day of the week.
    target_day: 1=Monday, 2=Tuesday, ..., 7=Sunday
    """
    base = datetime.strptime(base_date, "%Y-%m-%d")
    
    # Find the Monday of the week containing base_date
    days_since_monday = base.weekday()  # 0=Monday, 6=Sunday
    monday_of_week = base - timedelta(days=days_since_monday)
    
    # Calculate target date
    target_date = monday_of_week + timedelta(days=target_day - 1)
    
    return target_date.strftime("%Y-%m-%d")

def generate_attendance_time(schedule_time: str, date: str) -> str:
    """
    Generate a realistic attendance time close to the scheduled time.
    """
    # Parse the schedule time
    time_obj = datetime.strptime(schedule_time, "%H:%M:%S").time()
    
    # Add some random variation (±15 minutes)
    variation_minutes = random.randint(-15, 15)
    
    # Create datetime object for the specific date and time
    dt = datetime.combine(datetime.strptime(date, "%Y-%m-%d").date(), time_obj)
    dt += timedelta(minutes=variation_minutes)
    
    # Add random seconds
    dt += timedelta(seconds=random.randint(0, 59))
    
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def generate_curl_command_with_auth(account_id: str, date_time: str, time_table_id: str, 
                                   location_id: str, lat: float, lng: float, 
                                   account_name: str, schedule_name: str, bearer_token: str) -> str:
    """
    Generate a curl command for attendance request with real authentication.
    """
    data = {
        "account_id": account_id,
        "date_time": date_time,
        "time_table_id": time_table_id,
        "location_id": location_id,
        "image_url": "https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png",
        "lat": lat,
        "lng": lng
    }
    
    curl_command = f"""# {account_name} - {schedule_name} - {date_time}
curl --location 'http://localhost:9765/api/attendance/v1' \\
--header 'Content-Type: application/json' \\
--header 'Authorization: Bearer {bearer_token}' \\
--data '{json.dumps(data, separators=(",", ":"))}'
"""
    return curl_command

def generate_all_attendance_requests():
    """
    Generate all attendance requests using the actual data from the notebook.
    """
    # Base date for attendance
    base_date = "2025-06-23"  # Monday of current week
    
    # Use the actual access token from the notebook
    bearer_token = access_token
    
    print(f"Generating attendance requests with Bearer token: {bearer_token[:20]}...")
    print(f"Base date: {base_date}")
    print(f"Total employees: {len(employee_data)}")
    print(f"Total schedules: {len(schedule_list)}")
    print(f"Total requests to generate: {len(employee_data) * len(schedule_list)}")
    print("\n")
    
    all_commands = []
    request_count = 0
    
    # Generate header
    header = f"""# Attendance Requests with Real Authentication
# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Base date: {base_date}
# Total employees: {len(employee_data)}
# Total schedules: {len(schedule_list)}
# Total requests: {len(employee_data) * len(schedule_list)}
# Bearer token: {bearer_token[:20]}...

"""
    all_commands.append(header)
    
    # Generate requests for each employee and each schedule
    for employee in employee_data:
        account_id = employee['account_id']
        account_name = employee['name']
        
        section_header = f"## Requests for {account_name} ({account_id})\n\n"
        all_commands.append(section_header)
        
        for schedule in schedule_list:
            schedule_id = schedule['id']
            schedule_day = schedule['day']
            schedule_name = schedule['name']
            schedule_start_time = schedule['start_time']
            
            # Get the actual date for this day of the week
            actual_date = get_date_for_day(base_date, schedule_day)
            
            # Generate attendance time close to scheduled time
            attendance_time = generate_attendance_time(schedule_start_time, actual_date)
            
            # Use the first (and only) location
            location = location_list[0]
            
            # Generate curl command with real authentication
            curl_command = generate_curl_command_with_auth(
                account_id=account_id,
                date_time=attendance_time,
                time_table_id=schedule_id,
                location_id=location['id'],
                lat=location['lat'],
                lng=location['lng'],
                account_name=account_name,
                schedule_name=f"{schedule_name} (Day {schedule_day})",
                bearer_token=bearer_token
            )
            
            all_commands.append(curl_command)
            request_count += 1
        
        all_commands.append("\n")
    
    # Add footer
    footer = f"# Total requests generated: {request_count}\n"
    all_commands.append(footer)
    
    return ''.join(all_commands), request_count

# Generate all requests
print("Generating attendance requests with real authentication...")
all_curl_commands, total_requests = generate_all_attendance_requests()

# Save to file
filename = "executable_attendance_requests.txt"
with open(filename, 'w') as f:
    f.write(all_curl_commands)

print(f"\n✅ Successfully generated {total_requests} attendance requests!")
print(f"📁 Saved to: {filename}")
print(f"🔑 Using Bearer token: {access_token[:20]}...")
print(f"\n📋 File contains executable curl commands with real authentication.")
print(f"🚀 You can now execute these commands directly without token replacement.")