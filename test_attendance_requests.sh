#!/bin/bash

# Test script to verify attendance requests work correctly
# This script tests the first 5 curl commands to ensure they're working

echo "🧪 Testing attendance requests..."
echo "📁 Reading first 5 commands from: executable_attendance_requests_with_auth.txt"
echo ""

# Check if the file exists
if [ ! -f "executable_attendance_requests_with_auth.txt" ]; then
    echo "❌ Error: executable_attendance_requests_with_auth.txt not found!"
    exit 1
fi

# Initialize counters
success_count=0
error_count=0
test_count=0
max_tests=5

echo "⏳ Testing first $max_tests attendance requests..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Read and execute first few curl commands
while IFS= read -r line && [ $test_count -lt $max_tests ]; do
    # Skip comments and empty lines
    if [[ $line =~ ^#.*$ ]] || [[ -z "$line" ]]; then
        continue
    fi
    
    # Check if line starts with curl
    if [[ $line =~ ^curl.*$ ]]; then
        test_count=$((test_count + 1))
        
        # Extract employee info from previous comment
        employee_info=$(grep -B1 "^curl" executable_attendance_requests_with_auth.txt | grep "^#" | sed -n "${test_count}p" | sed 's/^# //')
        
        echo "[$test_count/$max_tests] Testing: $employee_info"
        
        # Execute the curl command and capture the response
        response=$(eval "$line" 2>&1)
        exit_code=$?
        
        echo "  Response: $response"
        
        if [ $exit_code -eq 0 ]; then
            # Check if response contains success indicators
            if echo "$response" | grep -q '"success":true\|"status":"success"\|"code":200\|SUCCESS'; then
                echo "  ✅ Success"
                success_count=$((success_count + 1))
            else
                echo "  ⚠️  Warning: Unexpected response"
                error_count=$((error_count + 1))
            fi
        else
            echo "  ❌ Error: Command failed with exit code $exit_code"
            error_count=$((error_count + 1))
        fi
        
        echo ""
        # Small delay between requests
        sleep 0.2
    fi
done < executable_attendance_requests_with_auth.txt

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 Test Results:"
echo "   Tests run: $test_count"
echo "   Successful: $success_count"
echo "   Errors/Warnings: $error_count"
echo ""

if [ $error_count -eq 0 ]; then
    echo "🎉 All test requests executed successfully!"
    echo "✅ Your attendance requests are working correctly."
    echo "💡 You can now run the full script: ./execute_attendance_requests.sh"
else
    echo "⚠️  Some test requests had issues."
    echo "🔧 Please check the responses above for debugging."
fi

echo ""
echo "✅ Test completed!"
