# Attendance Requests with Real Authentication
# Generated on: 2025-06-24 04:34:56
# Base date: 2025-06-24
# Total employees: 8
# Total schedules: 21
# Total requests: 168
# Bearer token: eyJhbGciOiJIUzI1NiJ9...
# Authentication URL: https://backend.kodekata.web.id/api/auth/v1/admin/sign-in
# Attendance API: http://localhost:9765/api/attendance/v1

## Requests for Rina Permadi (D1DCB8C61979434493A237363013D026)

# <PERSON><PERSON> Permadi - Clock in (Day 1) - 2025-06-23 07:11:07
echo "Executing: <PERSON><PERSON> Permadi - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-23 07:11:07","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 1) - 2025-06-23 12:13:06
echo "Executing: Rina Permadi - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-23 12:13:06","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 1) - 2025-06-23 16:57:47
echo "Executing: Rina Permadi - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-23 16:57:47","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 2) - 2025-06-24 07:02:12
echo "Executing: Rina Permadi - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-24 07:02:12","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 2) - 2025-06-24 12:12:59
echo "Executing: Rina Permadi - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-24 12:12:59","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 2) - 2025-06-24 17:10:48
echo "Executing: Rina Permadi - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-24 17:10:48","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 3) - 2025-06-25 06:47:29
echo "Executing: Rina Permadi - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-25 06:47:29","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 3) - 2025-06-25 11:46:52
echo "Executing: Rina Permadi - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-25 11:46:52","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 3) - 2025-06-25 17:06:48
echo "Executing: Rina Permadi - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-25 17:06:48","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 4) - 2025-06-26 07:09:34
echo "Executing: Rina Permadi - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-26 07:09:34","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 4) - 2025-06-26 11:47:29
echo "Executing: Rina Permadi - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-26 11:47:29","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 4) - 2025-06-26 17:06:00
echo "Executing: Rina Permadi - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-26 17:06:00","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 5) - 2025-06-27 07:01:44
echo "Executing: Rina Permadi - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-27 07:01:44","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 5) - 2025-06-27 11:56:20
echo "Executing: Rina Permadi - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-27 11:56:20","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 5) - 2025-06-27 17:02:49
echo "Executing: Rina Permadi - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-27 17:02:49","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 6) - 2025-06-28 07:06:25
echo "Executing: Rina Permadi - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-28 07:06:25","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 6) - 2025-06-28 12:09:55
echo "Executing: Rina Permadi - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-28 12:09:55","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 6) - 2025-06-28 17:07:16
echo "Executing: Rina Permadi - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-28 17:07:16","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 7) - 2025-06-29 06:45:48
echo "Executing: Rina Permadi - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-29 06:45:48","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 7) - 2025-06-29 12:04:05
echo "Executing: Rina Permadi - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-29 12:04:05","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 7) - 2025-06-29 16:54:53
echo "Executing: Rina Permadi - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D1DCB8C61979434493A237363013D026","date_time":"2025-06-29 16:54:53","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Wahyu Lestari (DB3A8625C1B64CD89935677A56981A6E)

# Wahyu Lestari - Clock in (Day 1) - 2025-06-23 06:56:18
echo "Executing: Wahyu Lestari - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-23 06:56:18","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 1) - 2025-06-23 12:02:50
echo "Executing: Wahyu Lestari - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-23 12:02:50","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 1) - 2025-06-23 17:04:27
echo "Executing: Wahyu Lestari - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-23 17:04:27","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 2) - 2025-06-24 07:07:00
echo "Executing: Wahyu Lestari - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-24 07:07:00","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 2) - 2025-06-24 12:08:03
echo "Executing: Wahyu Lestari - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-24 12:08:03","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 2) - 2025-06-24 17:06:15
echo "Executing: Wahyu Lestari - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-24 17:06:15","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 3) - 2025-06-25 07:10:58
echo "Executing: Wahyu Lestari - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-25 07:10:58","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 3) - 2025-06-25 12:05:56
echo "Executing: Wahyu Lestari - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-25 12:05:56","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 3) - 2025-06-25 17:07:35
echo "Executing: Wahyu Lestari - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-25 17:07:35","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 4) - 2025-06-26 07:08:00
echo "Executing: Wahyu Lestari - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-26 07:08:00","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 4) - 2025-06-26 11:56:16
echo "Executing: Wahyu Lestari - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-26 11:56:16","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 4) - 2025-06-26 16:48:42
echo "Executing: Wahyu Lestari - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-26 16:48:42","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 5) - 2025-06-27 07:07:31
echo "Executing: Wahyu Lestari - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-27 07:07:31","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 5) - 2025-06-27 12:05:37
echo "Executing: Wahyu Lestari - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-27 12:05:37","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 5) - 2025-06-27 16:47:32
echo "Executing: Wahyu Lestari - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-27 16:47:32","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 6) - 2025-06-28 07:07:56
echo "Executing: Wahyu Lestari - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-28 07:07:56","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 6) - 2025-06-28 12:10:01
echo "Executing: Wahyu Lestari - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-28 12:10:01","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 6) - 2025-06-28 17:04:35
echo "Executing: Wahyu Lestari - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-28 17:04:35","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - Clock in (Day 7) - 2025-06-29 06:50:56
echo "Executing: Wahyu Lestari - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-29 06:50:56","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - in day (Day 7) - 2025-06-29 12:08:50
echo "Executing: Wahyu Lestari - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-29 12:08:50","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Wahyu Lestari - clock out (Day 7) - 2025-06-29 17:04:06
echo "Executing: Wahyu Lestari - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"DB3A8625C1B64CD89935677A56981A6E","date_time":"2025-06-29 17:04:06","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Putra Setiawan (6D95AFF1ECD647B68BD2B37F0B0A7D55)

# Putra Setiawan - Clock in (Day 1) - 2025-06-23 07:12:30
echo "Executing: Putra Setiawan - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-23 07:12:30","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 1) - 2025-06-23 12:15:32
echo "Executing: Putra Setiawan - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-23 12:15:32","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 1) - 2025-06-23 17:01:22
echo "Executing: Putra Setiawan - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-23 17:01:22","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 2) - 2025-06-24 07:14:44
echo "Executing: Putra Setiawan - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-24 07:14:44","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 2) - 2025-06-24 12:11:56
echo "Executing: Putra Setiawan - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-24 12:11:56","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 2) - 2025-06-24 16:45:43
echo "Executing: Putra Setiawan - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-24 16:45:43","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 3) - 2025-06-25 07:04:13
echo "Executing: Putra Setiawan - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-25 07:04:13","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 3) - 2025-06-25 11:45:14
echo "Executing: Putra Setiawan - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-25 11:45:14","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 3) - 2025-06-25 16:50:04
echo "Executing: Putra Setiawan - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-25 16:50:04","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 4) - 2025-06-26 07:02:27
echo "Executing: Putra Setiawan - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-26 07:02:27","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 4) - 2025-06-26 12:03:12
echo "Executing: Putra Setiawan - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-26 12:03:12","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 4) - 2025-06-26 16:49:54
echo "Executing: Putra Setiawan - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-26 16:49:54","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 5) - 2025-06-27 07:04:07
echo "Executing: Putra Setiawan - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-27 07:04:07","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 5) - 2025-06-27 12:08:04
echo "Executing: Putra Setiawan - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-27 12:08:04","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 5) - 2025-06-27 16:57:14
echo "Executing: Putra Setiawan - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-27 16:57:14","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 6) - 2025-06-28 07:11:35
echo "Executing: Putra Setiawan - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-28 07:11:35","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 6) - 2025-06-28 12:10:04
echo "Executing: Putra Setiawan - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-28 12:10:04","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 6) - 2025-06-28 17:15:35
echo "Executing: Putra Setiawan - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-28 17:15:35","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - Clock in (Day 7) - 2025-06-29 06:47:00
echo "Executing: Putra Setiawan - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-29 06:47:00","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - in day (Day 7) - 2025-06-29 12:10:11
echo "Executing: Putra Setiawan - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-29 12:10:11","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Putra Setiawan - clock out (Day 7) - 2025-06-29 17:04:52
echo "Executing: Putra Setiawan - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"6D95AFF1ECD647B68BD2B37F0B0A7D55","date_time":"2025-06-29 17:04:52","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Eka Hidayat (D3EB0A34B74D4B4CAA9CB72E80C06904)

# Eka Hidayat - Clock in (Day 1) - 2025-06-23 07:13:57
echo "Executing: Eka Hidayat - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-23 07:13:57","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 1) - 2025-06-23 11:54:09
echo "Executing: Eka Hidayat - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-23 11:54:09","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 1) - 2025-06-23 17:09:27
echo "Executing: Eka Hidayat - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-23 17:09:27","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 2) - 2025-06-24 06:58:39
echo "Executing: Eka Hidayat - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-24 06:58:39","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 2) - 2025-06-24 11:45:30
echo "Executing: Eka Hidayat - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-24 11:45:30","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 2) - 2025-06-24 16:58:36
echo "Executing: Eka Hidayat - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-24 16:58:36","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 3) - 2025-06-25 06:47:57
echo "Executing: Eka Hidayat - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-25 06:47:57","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 3) - 2025-06-25 12:12:19
echo "Executing: Eka Hidayat - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-25 12:12:19","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 3) - 2025-06-25 17:08:28
echo "Executing: Eka Hidayat - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-25 17:08:28","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 4) - 2025-06-26 06:59:01
echo "Executing: Eka Hidayat - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-26 06:59:01","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 4) - 2025-06-26 12:04:22
echo "Executing: Eka Hidayat - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-26 12:04:22","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 4) - 2025-06-26 17:08:40
echo "Executing: Eka Hidayat - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-26 17:08:40","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 5) - 2025-06-27 07:12:55
echo "Executing: Eka Hidayat - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-27 07:12:55","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 5) - 2025-06-27 11:56:45
echo "Executing: Eka Hidayat - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-27 11:56:45","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 5) - 2025-06-27 17:13:08
echo "Executing: Eka Hidayat - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-27 17:13:08","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 6) - 2025-06-28 07:06:36
echo "Executing: Eka Hidayat - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-28 07:06:36","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 6) - 2025-06-28 12:09:17
echo "Executing: Eka Hidayat - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-28 12:09:17","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 6) - 2025-06-28 17:13:44
echo "Executing: Eka Hidayat - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-28 17:13:44","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - Clock in (Day 7) - 2025-06-29 06:59:10
echo "Executing: Eka Hidayat - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-29 06:59:10","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - in day (Day 7) - 2025-06-29 12:02:26
echo "Executing: Eka Hidayat - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-29 12:02:26","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Eka Hidayat - clock out (Day 7) - 2025-06-29 17:07:09
echo "Executing: Eka Hidayat - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"D3EB0A34B74D4B4CAA9CB72E80C06904","date_time":"2025-06-29 17:07:09","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Utami Halim (E97700D03D5C41B2A951403407C56F0C)

# Utami Halim - Clock in (Day 1) - 2025-06-23 07:07:12
echo "Executing: Utami Halim - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-23 07:07:12","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 1) - 2025-06-23 11:57:39
echo "Executing: Utami Halim - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-23 11:57:39","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 1) - 2025-06-23 16:45:48
echo "Executing: Utami Halim - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-23 16:45:48","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 2) - 2025-06-24 06:46:28
echo "Executing: Utami Halim - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-24 06:46:28","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 2) - 2025-06-24 11:50:37
echo "Executing: Utami Halim - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-24 11:50:37","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 2) - 2025-06-24 16:46:00
echo "Executing: Utami Halim - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-24 16:46:00","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 3) - 2025-06-25 07:02:38
echo "Executing: Utami Halim - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-25 07:02:38","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 3) - 2025-06-25 11:53:57
echo "Executing: Utami Halim - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-25 11:53:57","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 3) - 2025-06-25 17:14:15
echo "Executing: Utami Halim - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-25 17:14:15","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 4) - 2025-06-26 07:03:53
echo "Executing: Utami Halim - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-26 07:03:53","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 4) - 2025-06-26 12:02:51
echo "Executing: Utami Halim - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-26 12:02:51","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 4) - 2025-06-26 16:59:18
echo "Executing: Utami Halim - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-26 16:59:18","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 5) - 2025-06-27 07:01:56
echo "Executing: Utami Halim - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-27 07:01:56","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 5) - 2025-06-27 12:12:22
echo "Executing: Utami Halim - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-27 12:12:22","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 5) - 2025-06-27 16:57:46
echo "Executing: Utami Halim - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-27 16:57:46","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 6) - 2025-06-28 06:57:44
echo "Executing: Utami Halim - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-28 06:57:44","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 6) - 2025-06-28 11:55:54
echo "Executing: Utami Halim - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-28 11:55:54","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 6) - 2025-06-28 16:59:02
echo "Executing: Utami Halim - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-28 16:59:02","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - Clock in (Day 7) - 2025-06-29 06:54:38
echo "Executing: Utami Halim - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-29 06:54:38","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - in day (Day 7) - 2025-06-29 12:06:42
echo "Executing: Utami Halim - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-29 12:06:42","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Halim - clock out (Day 7) - 2025-06-29 17:11:16
echo "Executing: Utami Halim - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"E97700D03D5C41B2A951403407C56F0C","date_time":"2025-06-29 17:11:16","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for kevin sanjaya (0AC2B9213FEF433A82DEF9A41ED55E6D)

# kevin sanjaya - Clock in (Day 1) - 2025-06-23 06:49:13
echo "Executing: kevin sanjaya - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-23 06:49:13","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 1) - 2025-06-23 12:04:36
echo "Executing: kevin sanjaya - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-23 12:04:36","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 1) - 2025-06-23 16:47:15
echo "Executing: kevin sanjaya - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-23 16:47:15","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 2) - 2025-06-24 06:55:11
echo "Executing: kevin sanjaya - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-24 06:55:11","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 2) - 2025-06-24 11:51:05
echo "Executing: kevin sanjaya - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-24 11:51:05","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 2) - 2025-06-24 17:15:56
echo "Executing: kevin sanjaya - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-24 17:15:56","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 3) - 2025-06-25 07:15:36
echo "Executing: kevin sanjaya - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-25 07:15:36","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 3) - 2025-06-25 11:59:07
echo "Executing: kevin sanjaya - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-25 11:59:07","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 3) - 2025-06-25 16:52:54
echo "Executing: kevin sanjaya - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-25 16:52:54","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 4) - 2025-06-26 06:46:35
echo "Executing: kevin sanjaya - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-26 06:46:35","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 4) - 2025-06-26 11:51:36
echo "Executing: kevin sanjaya - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-26 11:51:36","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 4) - 2025-06-26 17:03:23
echo "Executing: kevin sanjaya - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-26 17:03:23","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 5) - 2025-06-27 07:06:42
echo "Executing: kevin sanjaya - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-27 07:06:42","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 5) - 2025-06-27 11:55:16
echo "Executing: kevin sanjaya - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-27 11:55:16","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 5) - 2025-06-27 16:53:39
echo "Executing: kevin sanjaya - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-27 16:53:39","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 6) - 2025-06-28 07:08:57
echo "Executing: kevin sanjaya - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-28 07:08:57","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 6) - 2025-06-28 12:02:02
echo "Executing: kevin sanjaya - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-28 12:02:02","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 6) - 2025-06-28 16:46:47
echo "Executing: kevin sanjaya - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-28 16:46:47","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - Clock in (Day 7) - 2025-06-29 06:58:23
echo "Executing: kevin sanjaya - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-29 06:58:23","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - in day (Day 7) - 2025-06-29 12:01:02
echo "Executing: kevin sanjaya - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-29 12:01:02","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# kevin sanjaya - clock out (Day 7) - 2025-06-29 16:47:11
echo "Executing: kevin sanjaya - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"0AC2B9213FEF433A82DEF9A41ED55E6D","date_time":"2025-06-29 16:47:11","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Utami Nurhalim (5970FF7088C54094A751BE29DFC6A930)

# Utami Nurhalim - Clock in (Day 1) - 2025-06-23 07:13:46
echo "Executing: Utami Nurhalim - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-23 07:13:46","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 1) - 2025-06-23 12:01:13
echo "Executing: Utami Nurhalim - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-23 12:01:13","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 1) - 2025-06-23 16:46:56
echo "Executing: Utami Nurhalim - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-23 16:46:56","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 2) - 2025-06-24 07:01:50
echo "Executing: Utami Nurhalim - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-24 07:01:50","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 2) - 2025-06-24 12:06:16
echo "Executing: Utami Nurhalim - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-24 12:06:16","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 2) - 2025-06-24 16:48:37
echo "Executing: Utami Nurhalim - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-24 16:48:37","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 3) - 2025-06-25 06:49:21
echo "Executing: Utami Nurhalim - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-25 06:49:21","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 3) - 2025-06-25 12:06:04
echo "Executing: Utami Nurhalim - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-25 12:06:04","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 3) - 2025-06-25 17:00:20
echo "Executing: Utami Nurhalim - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-25 17:00:20","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 4) - 2025-06-26 07:06:35
echo "Executing: Utami Nurhalim - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-26 07:06:35","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 4) - 2025-06-26 12:07:00
echo "Executing: Utami Nurhalim - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-26 12:07:00","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 4) - 2025-06-26 17:07:51
echo "Executing: Utami Nurhalim - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-26 17:07:51","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 5) - 2025-06-27 06:58:16
echo "Executing: Utami Nurhalim - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-27 06:58:16","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 5) - 2025-06-27 12:15:46
echo "Executing: Utami Nurhalim - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-27 12:15:46","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 5) - 2025-06-27 16:52:25
echo "Executing: Utami Nurhalim - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-27 16:52:25","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 6) - 2025-06-28 06:54:30
echo "Executing: Utami Nurhalim - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-28 06:54:30","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 6) - 2025-06-28 12:13:13
echo "Executing: Utami Nurhalim - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-28 12:13:13","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 6) - 2025-06-28 17:00:22
echo "Executing: Utami Nurhalim - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-28 17:00:22","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - Clock in (Day 7) - 2025-06-29 07:09:42
echo "Executing: Utami Nurhalim - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-29 07:09:42","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - in day (Day 7) - 2025-06-29 12:10:04
echo "Executing: Utami Nurhalim - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-29 12:10:04","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Utami Nurhalim - clock out (Day 7) - 2025-06-29 17:14:06
echo "Executing: Utami Nurhalim - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-29 17:14:06","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


## Requests for Rina Permadi (475E542E95654DC38A78969802284171)

# Rina Permadi - Clock in (Day 1) - 2025-06-23 06:55:34
echo "Executing: Rina Permadi - Clock in (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-23 06:55:34","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 1) - 2025-06-23 11:45:38
echo "Executing: Rina Permadi - in day (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-23 11:45:38","time_table_id":"A08312B07F58435F979757D508E8B179","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 1) - 2025-06-23 17:00:48
echo "Executing: Rina Permadi - clock out (Day 1)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-23 17:00:48","time_table_id":"3209149FE9944AA59D0BF0A557A03784","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 2) - 2025-06-24 06:57:09
echo "Executing: Rina Permadi - Clock in (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-24 06:57:09","time_table_id":"58B2B238466244618837A506471CB96A","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 2) - 2025-06-24 11:53:32
echo "Executing: Rina Permadi - in day (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-24 11:53:32","time_table_id":"0255FFC7709D4629A002C718E9AAD653","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 2) - 2025-06-24 16:50:57
echo "Executing: Rina Permadi - clock out (Day 2)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-24 16:50:57","time_table_id":"75921DE269CE49CDA9D56AA0E053BD61","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 3) - 2025-06-25 07:00:40
echo "Executing: Rina Permadi - Clock in (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-25 07:00:40","time_table_id":"4CF8C8CF0DC444378C96CD95C0F3487B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 3) - 2025-06-25 11:49:44
echo "Executing: Rina Permadi - in day (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-25 11:49:44","time_table_id":"5D62B8886C7F48D7950D3FFD94262FEA","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 3) - 2025-06-25 16:51:59
echo "Executing: Rina Permadi - clock out (Day 3)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-25 16:51:59","time_table_id":"EABDAA33F5C34C3E88FBE0EB4719BE59","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 4) - 2025-06-26 06:56:42
echo "Executing: Rina Permadi - Clock in (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-26 06:56:42","time_table_id":"4122D51716D2427D9C7FD25134D2223B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 4) - 2025-06-26 12:06:30
echo "Executing: Rina Permadi - in day (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-26 12:06:30","time_table_id":"4AFE9D2E06D54FEDA93804ED2541C461","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 4) - 2025-06-26 16:57:43
echo "Executing: Rina Permadi - clock out (Day 4)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-26 16:57:43","time_table_id":"800F2026ABF045A9B978C69BC1836629","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 5) - 2025-06-27 06:56:00
echo "Executing: Rina Permadi - Clock in (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-27 06:56:00","time_table_id":"46B2273984E74C4790573E8242611DA7","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 5) - 2025-06-27 12:09:10
echo "Executing: Rina Permadi - in day (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-27 12:09:10","time_table_id":"9B69474AF64447818955E0851E663158","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 5) - 2025-06-27 17:13:17
echo "Executing: Rina Permadi - clock out (Day 5)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-27 17:13:17","time_table_id":"7E69B88C77A041229765AFD1B880466D","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 6) - 2025-06-28 07:11:55
echo "Executing: Rina Permadi - Clock in (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-28 07:11:55","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 6) - 2025-06-28 12:00:07
echo "Executing: Rina Permadi - in day (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-28 12:00:07","time_table_id":"5F85D08A7135484AA9B0A53218EF3830","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 6) - 2025-06-28 17:03:44
echo "Executing: Rina Permadi - clock out (Day 6)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-28 17:03:44","time_table_id":"********************************","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - Clock in (Day 7) - 2025-06-29 06:47:16
echo "Executing: Rina Permadi - Clock in (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-29 06:47:16","time_table_id":"387AC3A0F7414A9A90E626006954B4E1","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - in day (Day 7) - 2025-06-29 12:10:16
echo "Executing: Rina Permadi - in day (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-29 12:10:16","time_table_id":"A08EE86781CE403B8662243C1F6CBECD","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""

# Rina Permadi - clock out (Day 7) - 2025-06-29 17:14:51
echo "Executing: Rina Permadi - clock out (Day 7)"
response=$(curl -s --location 'http://localhost:9765/api/attendance/v1' --header 'Content-Type: application/json' --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************_**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FZEorBq-gC7glCq9UlPUIOHJw41QJI4pS2RFx_z06BU' --data '{"account_id":"475E542E95654DC38A78969802284171","date_time":"2025-06-29 17:14:51","time_table_id":"A0341403597E479892488621FA1E734B","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}')
echo "Response: $response"
if echo "$response" | grep -q '"success":true\|SUCCESS'; then
    echo "✅ Success"
else
    echo "⚠️ Warning: Unexpected response"
fi
echo ""


# Total requests generated: 168
# All curl commands above contain real Bearer tokens and are ready to execute
# To execute all commands, you can run: bash executable_attendance_requests_with_auth.txt
