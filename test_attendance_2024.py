#!/usr/bin/env python3
"""
Test version - Generate attendance for first week of January 2024 only
This is a test to verify the full year script works correctly before running the complete dataset.
"""

import requests
import random
import time
from datetime import datetime, timedelta, date
import calendar

# Configuration
WORK_UNIT_ID = '3711AD05A5234B46858CB9CA5FE32ECD'
BASE_URL = "https://backend.kodekata.web.id/api"
SIGN_IN_URL = "/auth/v1/admin/sign-in"
ATTENDANCE_URL = "http://localhost:9765/api/attendance/v1"
IMAGE_URL = "https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png"

def authenticate():
    """Authenticate and get access token"""
    data = {"data": "<EMAIL>", "password": "admin"}
    response = requests.post(f"{BASE_URL}{SIGN_IN_URL}", json=data)
    return response.json()["response_data"]["access_token"]

def fetch_data(access_token):
    """Fetch employee, schedule, and location data"""
    headers = {'Authorization': f'Bearer {access_token}'}
    
    employee_data = requests.get(f'{BASE_URL}/employee/v1/work-unit/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    schedule_list = requests.get(f'{BASE_URL}/work-unit/v1/schedule/regular/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    location_list = requests.get(f'{BASE_URL}/work-unit/v1/location/{WORK_UNIT_ID}', headers=headers).json()["response_data"]
    
    return employee_data, schedule_list, location_list

def get_test_working_days():
    """Get working days for first week of January 2024 (for testing)"""
    working_days = []
    
    # First week of January 2024: Jan 2-5 (Tuesday-Friday, since Jan 1 is Monday/holiday)
    start_date = date(2024, 1, 2)  # Tuesday
    end_date = date(2024, 1, 5)    # Friday
    
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday to Friday
            working_days.append(current_date)
        current_date += timedelta(days=1)
    
    return working_days

def generate_attendance_time(schedule_time: str, target_date: date) -> str:
    """Generate realistic attendance time for a specific date"""
    time_obj = datetime.strptime(schedule_time, "%H:%M:%S").time()
    variation_minutes = random.randint(-15, 15)
    dt = datetime.combine(target_date, time_obj)
    dt += timedelta(minutes=variation_minutes, seconds=random.randint(0, 59))
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def execute_attendance_request(request_data):
    """Execute single attendance request"""
    data = {
        "account_id": request_data['account_id'],
        "date_time": request_data['date_time'],
        "time_table_id": request_data['time_table_id'],
        "location_id": request_data['location_id'],
        "image_url": IMAGE_URL,
        "lat": request_data['lat'],
        "lng": request_data['lng']
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {request_data["bearer_token"]}'
    }
    
    try:
        response = requests.post(ATTENDANCE_URL, json=data, headers=headers)
        response_data = response.json()
        
        if response.status_code == 200 and response_data.get('success') == True:
            return {"status": "success", "response": response_data}
        elif 'ALREADY_ATTENDANCE' in str(response_data):
            return {"status": "already_exists", "response": response_data}
        else:
            return {"status": "warning", "response": response_data}
            
    except Exception as e:
        return {"status": "error", "response": str(e)}

def main():
    """Test execution for first week of January 2024"""
    print("🧪 Test Attendance Request Executor - First Week January 2024")
    print("=" * 70)
    
    # Step 1: Authentication
    print("🔐 Step 1: Authenticating...")
    try:
        access_token = authenticate()
        print(f"✅ Authentication successful! Token: {access_token[:20]}...")
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False
    
    # Step 2: Fetch data
    print("\n📊 Step 2: Fetching data...")
    try:
        employee_data, schedule_list, location_list = fetch_data(access_token)
        print(f"✅ Data fetched successfully!")
        print(f"   - Employees: {len(employee_data)}")
        print(f"   - Schedules: {len(schedule_list)}")
        print(f"   - Locations: {len(location_list)}")
    except Exception as e:
        print(f"❌ Data fetching failed: {e}")
        return False
    
    # Step 3: Calculate test working days
    print("\n📅 Step 3: Calculating test working days...")
    working_days = get_test_working_days()
    
    # Get unique schedule names
    unique_schedule_names = []
    main_schedules = []
    
    for schedule in schedule_list:
        if schedule['name'] not in unique_schedule_names:
            unique_schedule_names.append(schedule['name'])
            main_schedules.append(schedule)
    
    main_schedules = main_schedules[:3]  # Use first 3 unique schedules
    
    total_requests = len(employee_data) * len(working_days) * len(main_schedules)
    
    print(f"✅ Test parameters:")
    print(f"   - Test working days: {len(working_days)} ({working_days[0]} to {working_days[-1]})")
    print(f"   - Checkpoints per day: {len(main_schedules)} ({[s['name'] for s in main_schedules]})")
    print(f"   - Total test requests: {total_requests}")
    
    # Step 4: Prepare requests
    print(f"\n📝 Step 4: Preparing {total_requests} test requests...")
    
    all_requests = []
    location = location_list[0]
    
    for employee in employee_data:
        for working_day in working_days:
            for schedule in main_schedules:
                attendance_time = generate_attendance_time(schedule['start_time'], working_day)
                
                request_data = {
                    'account_id': employee['account_id'],
                    'date_time': attendance_time,
                    'time_table_id': schedule['id'],
                    'location_id': location['id'],
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'account_name': employee['name'],
                    'schedule_name': f"{schedule['name']} ({working_day.strftime('%Y-%m-%d')})",
                    'bearer_token': access_token
                }
                all_requests.append(request_data)
    
    print(f"✅ {len(all_requests)} test requests prepared")
    
    # Step 5: Execute requests
    print(f"\n🚀 Step 5: Executing {len(all_requests)} test requests...")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    results = {'success': 0, 'already_exists': 0, 'warning': 0, 'error': 0, 'details': []}
    start_time = time.time()
    
    for i, request_data in enumerate(all_requests, 1):
        print(f"[{i:2d}/{len(all_requests)}] {request_data['account_name']} - {request_data['schedule_name']}")
        
        result = execute_attendance_request(request_data)
        results[result['status']] += 1
        results['details'].append({
            'employee': request_data['account_name'],
            'schedule': request_data['schedule_name'],
            'status': result['status'],
            'response': result['response']
        })
        
        if result['status'] == 'success':
            print("   ✅ Success")
        elif result['status'] == 'already_exists':
            print("   ⚠️  Already exists")
        elif result['status'] == 'warning':
            print("   ⚠️  Warning")
        else:
            print("   ❌ Error")
        
        time.sleep(0.1)  # Small delay
    
    execution_time = time.time() - start_time
    
    # Results summary
    print("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("📊 TEST EXECUTION SUMMARY")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"   Total requests executed: {len(all_requests)}")
    print(f"   ✅ Successful (new records): {results['success']}")
    print(f"   ⚠️  Already exists: {results['already_exists']}")
    print(f"   ⚠️  Warnings: {results['warning']}")
    print(f"   ❌ Errors: {results['error']}")
    print(f"   ⏱️  Execution time: {execution_time:.2f} seconds")
    
    total_completed = results['success'] + results['already_exists']
    success_rate = (total_completed / len(all_requests)) * 100
    print(f"\n🎯 COMPLETION RATE: {success_rate:.1f}%")
    
    if results['error'] == 0:
        print("\n🎉 TEST COMPLETED SUCCESSFULLY!")
        print("✅ The full year script should work correctly.")
        print(f"📈 Projected full year: {total_requests * (259/len(working_days)):.0f} requests")
    else:
        print(f"\n⚠️  {results['error']} requests failed in test")
    
    return results

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user.")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
