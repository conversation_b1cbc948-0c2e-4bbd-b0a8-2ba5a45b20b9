# Attendance Requests Generator

## Overview
This project generates comprehensive attendance requests for all employees and all schedules using data from the notebook. The generated requests follow the specified curl command structure and meet all the requirements.

## Generated Data Summary

### Employee Data (8 employees)
1. **<PERSON><PERSON><PERSON>** (5970FF7088C54094A751BE29DFC6A930)
2. **<PERSON><PERSON>** (D1DCB8C61979434493A237363013D026)
3. **<PERSON><PERSON>** (475E542E95654DC38A78969802284171) - Different account
4. **Wahyu Lestari** (DB3A8625C1B64CD89935677A56981A6E)
5. **<PERSON><PERSON>iawan** (6D95AFF1ECD647B68BD2B37F0B0A7D55)
6. **Eka Hidayat** (D3EB0A34B74D4B4CAA9CB72E80C06904)
7. **<PERSON><PERSON><PERSON>** (E97700D03D5C41B2A951403407C56F0C)
8. **kevin sanjaya** (0AC2B9213FEF433A82DEF9A41ED55E6D)

### Schedule Data (21 schedules across 7 days)
Each day has 3 schedules:
- **Clock in**: 07:00:00 - 08:00:00
- **In day**: 12:00:00 - 13:00:00 (lunch break return)
- **Clock out**: 17:00:00 - 18:00:00

Days covered: Monday (1) through Sunday (7)

### Location Data
- **Kantor** (628C9E37CD02456B857419BF4120CCBA)
- Coordinates: lat: -8.**************, lng: 120.**************
- Radius: 200 meters

## Key Features

### 1. **Complete Coverage**
- **Total requests generated**: 168 (8 employees × 21 schedules)
- Every employee has attendance requests for every schedule

### 2. **Date Accuracy**
- **Base date**: 2025-06-23 (Monday)
- Dates are calculated correctly for each day of the week
- Monday (Day 1) = 2025-06-23
- Tuesday (Day 2) = 2025-06-24
- Wednesday (Day 3) = 2025-06-25
- Thursday (Day 4) = 2025-06-26
- Friday (Day 5) = 2025-06-27
- Saturday (Day 6) = 2025-06-28
- Sunday (Day 7) = 2025-06-29

### 3. **Time Accuracy**
- Attendance times are close to scheduled times with realistic variations
- Random variation of ±15 minutes from scheduled time
- Random seconds added for realism
- Examples:
  - Clock in (07:00): Generated times like 06:53:50, 07:09:23, etc.
  - In day (12:00): Generated times like 11:59:00, 12:15:39, etc.
  - Clock out (17:00): Generated times like 17:15:05, 16:53:09, etc.

### 4. **Location Selection**
- Uses the single available location "Kantor"
- Coordinates match the reference point exactly (-8.**************, 120.**************)
- All requests use the same geographically appropriate location

### 5. **Request Structure**
Each curl request includes:
- **account_id**: Employee's unique identifier
- **date_time**: Realistic attendance timestamp
- **time_table_id**: Schedule's unique identifier
- **location_id**: Location's unique identifier
- **image_url**: Standard company logo URL
- **lat/lng**: Exact coordinates matching the reference point

## Files Generated

1. **generate_attendance_requests.py**: The main script that generates all requests
2. **attendance_requests.txt**: Complete output with all 168 curl commands
3. **README_attendance_requests.md**: This documentation file

## Usage

### To regenerate requests:
```bash
python generate_attendance_requests.py
```

### To save output to file:
```bash
python generate_attendance_requests.py > attendance_requests.txt
```

### To modify the base date:
Edit the `base_date` variable in the `main()` function of `generate_attendance_requests.py`

## Request Format Example

```bash
# Utami Nurhalim - Clock in (Day 1) - 2025-06-23 06:53:50
curl --location 'http://localhost:9765/api/attendance/v1' \
--header 'Content-Type: application/json' \
--header 'Authorization: ••••••' \
--data '{"account_id":"5970FF7088C54094A751BE29DFC6A930","date_time":"2025-06-23 06:53:50","time_table_id":"9537445F75F8449181AD89D220F5A4C3","location_id":"628C9E37CD02456B857419BF4120CCBA","image_url":"https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png","lat":-8.**************,"lng":120.**************}'
```

## Notes

- **Authorization header**: Replace `••••••` with actual Bearer token
- **Server URL**: Currently set to `http://localhost:9765/api/attendance/v1`
- **Image URL**: Uses staging environment logo
- **Coordinates**: All requests use the exact reference coordinates provided
- **Time zones**: Times are in local format (no timezone specified)

## Validation

✅ **Data Source**: Uses all schedule and account data from notebook  
✅ **Request Coverage**: Creates requests for every schedule entry for every account  
✅ **Date Parameter**: Uses 2025-06-23 as baseline for attendance timestamps  
✅ **Location Selection**: Uses the single available location with correct coordinates  
✅ **Time Accuracy**: Attendance times are close to scheduled times with realistic variation  
✅ **Geographic Accuracy**: Location coordinates match the reference point exactly  
✅ **Request Format**: Follows the provided curl command structure exactly
