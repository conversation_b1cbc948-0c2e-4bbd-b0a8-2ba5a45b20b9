#!/usr/bin/env python3
"""
Generate attendance requests for all schedules and all accounts using notebook data.
This script creates curl commands for every schedule entry for every account.
"""

import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Data extracted from the notebook
EMPLOYEE_DATA = [
    {'account_id': '5970FF7088C54094A751BE29DFC6A930', 'name': '<PERSON><PERSON><PERSON>'},
    {'account_id': 'D1DCB8C61979434493A237363013D026', 'name': '<PERSON><PERSON>madi'},
    {'account_id': '475E542E95654DC38A78969802284171', 'name': '<PERSON><PERSON> Permadi'},
    {'account_id': 'DB3A8625C1B64CD89935677A56981A6E', 'name': 'Wahyu Lestari'},
    {'account_id': '6D95AFF1ECD647B68BD2B37F0B0A7D55', 'name': '<PERSON><PERSON>'},
    {'account_id': 'D3EB0A34B74D4B4CAA9CB72E80C06904', 'name': '<PERSON><PERSON> Hidayat'},
    {'account_id': 'E97700D03D5C41B2A951403407C56F0C', 'name': 'Utami Halim'},
    {'account_id': '0AC2B9213FEF433A82DEF9A41ED55E6D', 'name': 'kevin sanjaya'}
]

SCHEDULE_DATA = [
    # Monday (day 1)
    {'id': '9537445F75F8449181AD89D220F5A4C3', 'day': 1, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': 'A08312B07F58435F979757D508E8B179', 'day': 1, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': '3209149FE9944AA59D0BF0A557A03784', 'day': 1, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Tuesday (day 2)
    {'id': '58B2B238466244618837A506471CB96A', 'day': 2, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': '0255FFC7709D4629A002C718E9AAD653', 'day': 2, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': '75921DE269CE49CDA9D56AA0E053BD61', 'day': 2, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Wednesday (day 3)
    {'id': '4CF8C8CF0DC444378C96CD95C0F3487B', 'day': 3, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': '5D62B8886C7F48D7950D3FFD94262FEA', 'day': 3, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': 'EABDAA33F5C34C3E88FBE0EB4719BE59', 'day': 3, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Thursday (day 4)
    {'id': '4122D51716D2427D9C7FD25134D2223B', 'day': 4, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': '4AFE9D2E06D54FEDA93804ED2541C461', 'day': 4, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': '800F2026ABF045A9B978C69BC1836629', 'day': 4, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Friday (day 5)
    {'id': '46B2273984E74C4790573E8242611DA7', 'day': 5, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': '9B69474AF64447818955E0851E663158', 'day': 5, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': '7E69B88C77A041229765AFD1B880466D', 'day': 5, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Saturday (day 6)
    {'id': 'DE90DCC05BD44DDE9B67D7D27BEC9AD8', 'day': 6, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': '5F85D08A7135484AA9B0A53218EF3830', 'day': 6, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': 'BE888B931D834E14A449ECEE87DF5446', 'day': 6, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'},
    # Sunday (day 7)
    {'id': '387AC3A0F7414A9A90E626006954B4E1', 'day': 7, 'name': 'Clock in', 'start_time': '07:00:00', 'end_time': '08:00:00'},
    {'id': 'A08EE86781CE403B8662243C1F6CBECD', 'day': 7, 'name': 'in day', 'start_time': '12:00:00', 'end_time': '13:00:00'},
    {'id': 'A0341403597E479892488621FA1E734B', 'day': 7, 'name': 'clock out', 'start_time': '17:00:00', 'end_time': '18:00:00'}
]

LOCATION_DATA = [
    {'id': '628C9E37CD02456B857419BF4120CCBA', 'name': 'Kantor', 'lat': -8.61252254868111, 'lng': 120.46346515417102}
]

# Reference coordinates for location selection
REFERENCE_LAT = -8.61252254868111
REFERENCE_LNG = 120.46346515417102

# Base URL and image URL
BASE_URL = "http://localhost:9765/api/attendance/v1"
IMAGE_URL = "https://staging-nucalale.kodekata.web.id/assets/logo/lg_brand.png"

def get_date_for_day(base_date: str, target_day: int) -> str:
    """
    Get the date string for a specific day of the week.
    target_day: 1=Monday, 2=Tuesday, ..., 7=Sunday
    """
    base = datetime.strptime(base_date, "%Y-%m-%d")
    
    # Find the Monday of the week containing base_date
    days_since_monday = base.weekday()  # 0=Monday, 6=Sunday
    monday_of_week = base - timedelta(days=days_since_monday)
    
    # Calculate target date
    target_date = monday_of_week + timedelta(days=target_day - 1)
    
    return target_date.strftime("%Y-%m-%d")

def generate_attendance_time(schedule_time: str, date: str) -> str:
    """
    Generate a realistic attendance time close to the scheduled time.
    """
    # Parse the schedule time
    time_obj = datetime.strptime(schedule_time, "%H:%M:%S").time()
    
    # Add some random variation (±15 minutes)
    variation_minutes = random.randint(-15, 15)
    
    # Create datetime object for the specific date and time
    dt = datetime.combine(datetime.strptime(date, "%Y-%m-%d").date(), time_obj)
    dt += timedelta(minutes=variation_minutes)
    
    # Add random seconds
    dt += timedelta(seconds=random.randint(0, 59))
    
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def select_location() -> Dict[str, Any]:
    """
    Select a location that's geographically close to the reference coordinates.
    For now, we only have one location which is already at the reference point.
    """
    return LOCATION_DATA[0]

def generate_curl_command(account_id: str, date_time: str, time_table_id: str, 
                         location_id: str, lat: float, lng: float, 
                         account_name: str, schedule_name: str) -> str:
    """
    Generate a curl command for attendance request.
    """
    data = {
        "account_id": account_id,
        "date_time": date_time,
        "time_table_id": time_table_id,
        "location_id": location_id,
        "image_url": IMAGE_URL,
        "lat": lat,
        "lng": lng
    }
    
    curl_command = f"""# {account_name} - {schedule_name} - {date_time}
curl --location '{BASE_URL}' \\
--header 'Content-Type: application/json' \\
--header 'Authorization: ••••••' \\
--data '{json.dumps(data, separators=(",", ":"))}'
"""
    return curl_command

def main():
    """
    Generate attendance requests for all schedules and all accounts.
    """
    # Base date for attendance (you can modify this)
    base_date = "2025-06-23"  # Monday of current week
    
    print(f"# Attendance Requests Generator")
    print(f"# Base date: {base_date}")
    print(f"# Total employees: {len(EMPLOYEE_DATA)}")
    print(f"# Total schedules: {len(SCHEDULE_DATA)}")
    print(f"# Total requests to generate: {len(EMPLOYEE_DATA) * len(SCHEDULE_DATA)}")
    print(f"")
    
    request_count = 0
    
    # Generate requests for each employee and each schedule
    for employee in EMPLOYEE_DATA:
        account_id = employee['account_id']
        account_name = employee['name']
        
        print(f"## Requests for {account_name} ({account_id})")
        print("")
        
        for schedule in SCHEDULE_DATA:
            schedule_id = schedule['id']
            schedule_day = schedule['day']
            schedule_name = schedule['name']
            schedule_start_time = schedule['start_time']
            
            # Get the actual date for this day of the week
            actual_date = get_date_for_day(base_date, schedule_day)
            
            # Generate attendance time close to scheduled time
            attendance_time = generate_attendance_time(schedule_start_time, actual_date)
            
            # Select location (currently only one available)
            location = select_location()
            
            # Generate curl command
            curl_command = generate_curl_command(
                account_id=account_id,
                date_time=attendance_time,
                time_table_id=schedule_id,
                location_id=location['id'],
                lat=location['lat'],
                lng=location['lng'],
                account_name=account_name,
                schedule_name=f"{schedule_name} (Day {schedule_day})"
            )
            
            print(curl_command)
            request_count += 1
        
        print("")
    
    print(f"# Total requests generated: {request_count}")

if __name__ == "__main__":
    main()
